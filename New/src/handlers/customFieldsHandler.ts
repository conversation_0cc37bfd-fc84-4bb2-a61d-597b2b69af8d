/**
 * Custom Fields Synchronization Handler
 *
 * Provides comprehensive custom field synchronization between AutoPatient (AP)
 * and CliniCore (CC) platforms with intelligent field matching, database upserts,
 * and detailed tracking of matched/unmatched fields.
 *
 * Features:
 * - Fuzzy field matching with normalization
 * - Bidirectional field mapping storage
 * - Comprehensive statistics and error tracking
 * - TypeScript strict compliance
 * - Request ID tracing for debugging
 */

import apiClient from "@apiClient";
import { dbSchema, getDb } from "@database";
import type { APGetCustomFieldType, GetCCCustomField } from "@type";
import { eq } from "drizzle-orm";
import type { Context } from "hono";
import { logError as logDbError } from "@/utils/errorLogger";
import { logDebug, logError, logInfo } from "@/utils/logger";
import { matchString } from "@/utils/matchString";

/**
 * Custom field synchronization response interface
 */
export interface CustomFieldSyncResponse {
	/** Number of successfully matched field pairs */
	matchedCount: number;
	/** Number of successfully upserted database records */
	upsertedCount: number;
	/** Array of AP fields that couldn't be matched */
	unmatchedApFields: APGetCustomFieldType[];
	/** Array of CC fields that couldn't be matched */
	unmatchedCcFields: GetCCCustomField[];
	/** Comprehensive processing statistics */
	statistics: {
		totalApFields: number;
		totalCcFields: number;
		totalProcessed: number;
		totalMatched: number;
		totalUnmatched: number;
	};
	/** Array of errors encountered during processing */
	errors: string[];
}

/**
 * Database record type for custom field mappings
 */
type CustomFieldInsert = typeof dbSchema.customFields.$inferInsert;

/**
 * Determine if two custom fields match using fuzzy comparison
 *
 * Compares fields by name, label, and fieldKey properties using normalized
 * string matching that handles case differences, special characters, and umlauts.
 *
 * @param apField - AutoPatient custom field object
 * @param ccField - CliniCore custom field object
 * @returns True if fields are considered a match, false otherwise
 */
function fieldsMatch(
	apField: APGetCustomFieldType,
	ccField: GetCCCustomField,
): boolean {
	// Compare AP name with CC name and label
	if (
		matchString(apField.name, ccField.name) ||
		matchString(apField.name, ccField.label)
	) {
		return true;
	}

	// Compare AP fieldKey with CC name and label (if fieldKey exists)
	if (apField.fieldKey) {
		if (
			matchString(apField.fieldKey, ccField.name) ||
			matchString(apField.fieldKey, ccField.label)
		) {
			return true;
		}
	}

	return false;
}

/**
 * Comprehensive custom field synchronization function
 *
 * Fetches custom fields from both platforms, performs intelligent matching,
 * and maintains bidirectional field mappings in the local database.
 *
 * @param requestId - Request ID for tracing and logging
 * @returns Promise resolving to comprehensive synchronization results
 */
export async function synchronizeCustomFields(
	requestId: string,
): Promise<CustomFieldSyncResponse> {
	logInfo("Starting custom field synchronization", { requestId });

	const response: CustomFieldSyncResponse = {
		matchedCount: 0,
		upsertedCount: 0,
		unmatchedApFields: [],
		unmatchedCcFields: [],
		statistics: {
			totalApFields: 0,
			totalCcFields: 0,
			totalProcessed: 0,
			totalMatched: 0,
			totalUnmatched: 0,
		},
		errors: [],
	};

	try {
		// Phase 1: Data Fetching
		logDebug("Fetching custom fields from both platforms", { requestId });

		const [apCustomFields, ccCustomFields, existingMappings] =
			await Promise.all([
				apiClient.ap.apCustomfield.all(),
				apiClient.cc.ccCustomfieldReq.all(),
				getDb().select().from(dbSchema.customFields),
			]);

		logInfo("Data fetching completed", {
			requestId,
			apFieldsCount: apCustomFields.length,
			ccFieldsCount: ccCustomFields.length,
			existingMappingsCount: existingMappings.length,
		});

		// Update statistics
		response.statistics.totalApFields = apCustomFields.length;
		response.statistics.totalCcFields = ccCustomFields.length;
		response.statistics.totalProcessed =
			apCustomFields.length + ccCustomFields.length;

		// Phase 2: Field Matching and Database Operations
		const processedCcFields = new Set<number>();
		const db = getDb();

		for (const apField of apCustomFields) {
			let matchFound = false;

			logDebug("Processing AP field", {
				requestId,
				apFieldId: apField.id,
				apFieldName: apField.name,
				apFieldType: apField.dataType,
			});

			// Try to find a matching CC field
			for (const ccField of ccCustomFields) {
				if (processedCcFields.has(ccField.id)) {
					continue; // Skip already matched CC fields
				}

				if (fieldsMatch(apField, ccField)) {
					logDebug("Field match found", {
						requestId,
						apFieldId: apField.id,
						apFieldName: apField.name,
						ccFieldId: ccField.id,
						ccFieldName: ccField.name,
						ccFieldLabel: ccField.label,
					});

					try {
						// Check if mapping already exists
						const existingMapping = existingMappings.find(
							(mapping) =>
								mapping.apId === apField.id || mapping.ccId === ccField.id,
						);

						const mappingData: CustomFieldInsert = {
							apId: apField.id,
							ccId: ccField.id,
							name: apField.name,
							label: ccField.label,
							type: apField.dataType,
							apConfig: apField,
							ccConfig: ccField,
						};

						if (existingMapping) {
							// Update existing mapping
							await db
								.update(dbSchema.customFields)
								.set({
									...mappingData,
									updatedAt: new Date(),
								})
								.where(eq(dbSchema.customFields.id, existingMapping.id));

							logDebug("Updated existing field mapping", {
								requestId,
								mappingId: existingMapping.id,
								apFieldId: apField.id,
								ccFieldId: ccField.id,
							});
						} else {
							// Insert new mapping
							await db.insert(dbSchema.customFields).values(mappingData);

							logDebug("Created new field mapping", {
								requestId,
								apFieldId: apField.id,
								ccFieldId: ccField.id,
							});
						}

						response.upsertedCount++;
						processedCcFields.add(ccField.id);
						matchFound = true;
						break; // Move to next AP field
					} catch (error) {
						const errorMessage = `Failed to upsert field mapping: AP ${apField.id} -> CC ${ccField.id}`;
						logError(errorMessage, error);
						await logDbError(
							error instanceof Error ? error : new Error(String(error)),
							{
								type: "custom_field_sync",
								data: {
									apFieldId: apField.id,
									ccFieldId: ccField.id,
									operation: "upsert_mapping",
								},
							},
						);
						response.errors.push(errorMessage);
					}
				}
			}

			if (!matchFound) {
				response.unmatchedApFields.push(apField);
				logDebug("No match found for AP field", {
					requestId,
					apFieldId: apField.id,
					apFieldName: apField.name,
				});
			} else {
				response.matchedCount++;
			}
		}

		// Phase 3: Track unmatched CC fields
		for (const ccField of ccCustomFields) {
			if (!processedCcFields.has(ccField.id)) {
				response.unmatchedCcFields.push(ccField);
				logDebug("No match found for CC field", {
					requestId,
					ccFieldId: ccField.id,
					ccFieldName: ccField.name,
					ccFieldLabel: ccField.label,
				});
			}
		}

		// Update final statistics
		response.statistics.totalMatched = response.matchedCount;
		response.statistics.totalUnmatched =
			response.unmatchedApFields.length + response.unmatchedCcFields.length;

		logInfo("Custom field synchronization completed", {
			requestId,
			matchedCount: response.matchedCount,
			upsertedCount: response.upsertedCount,
			unmatchedApCount: response.unmatchedApFields.length,
			unmatchedCcCount: response.unmatchedCcFields.length,
			errorsCount: response.errors.length,
		});

		return response;
	} catch (error) {
		const errorMessage = "Custom field synchronization failed";
		logError(errorMessage, error);
		await logDbError(
			error instanceof Error ? error : new Error(String(error)),
			{
				type: "custom_field_sync",
				data: { operation: "synchronize_custom_fields" },
			},
		);
		response.errors.push(errorMessage);
		return response;
	}
}

/**
 * Handle Custom Fields webhook events
 *
 * @param c - Hono context object
 * @returns JSON response with processing results
 */
export async function cfHandler(c: Context): Promise<Response> {
	const requestId = c.get("requestId");

	try {
		const syncResult = await synchronizeCustomFields(requestId);

		return c.json(
			{
				message: "Custom field synchronization completed",
				requestId,
				timestamp: new Date().toISOString(),
				result: syncResult,
			},
			200,
		);
	} catch (error) {
		logError("Custom field handler failed", error);
		return c.json(
			{
				error: "Custom field synchronization failed",
				details: String(error),
				requestId,
				timestamp: new Date().toISOString(),
			},
			500,
		);
	}
}
