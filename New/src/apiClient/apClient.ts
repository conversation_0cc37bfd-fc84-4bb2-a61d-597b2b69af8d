/**
 * AutoPatient (AP) API Client
 *
 * Comprehensive API client for AutoPatient system providing all CRUD operations
 * for contacts, appointments, custom fields, and notes.
 *
 * Features:
 * - Complete feature parity with v3Integration
 * - TypeScript typing and error handling
 * - Configuration from configs.ts
 * - Data cleaning utilities
 */

import getConfig from "@config";
import type {
	APGetCustomFieldType,
	APPostCustomfieldType,
	GetAPAppointmentType,
	GetAPContactType,
	GetAPNoteType,
	PostAPAppointmentType,
	PostAPContactType,
	PutAPAppointmentType,
} from "@type/APTypes";
import { apiResponseCache } from "@utils/advancedCache";
import cleanData from "@utils/cleanData";
import { logDebug } from "@/utils/logger";

/**
 * HTTP request options interface
 */
interface RequestOptions {
	url: string;
	method: "GET" | "POST" | "PUT" | "DELETE";
	data?: unknown;
	params?: Record<string, string | number | boolean>;
	invalidateCache?: boolean;
}

/**
 * Standard API response interface for delete operations
 */
interface DeleteResponse extends Record<string, unknown> {
	succeded: boolean;
}

/**
 * Standard API response interface for appointment operations
 */
interface AppointmentUpdateResponse extends Record<string, unknown> {
	appointment: GetAPAppointmentType;
}

/**
 * Base HTTP client for AutoPatient API v2 with intelligent caching
 */
const apRequestV2 = async <
	T extends Record<string, unknown> = Record<string, unknown>,
>(
	options: RequestOptions,
): Promise<T> => {
	const { url, method, data, params, invalidateCache = false } = options;

	// Build full URL with base domain
	const baseUrl = getConfig("apApiDomain");
	let fullUrl = `${baseUrl}${url}`;

	// Add query parameters if provided
	if (params) {
		const searchParams = new URLSearchParams();
		Object.entries(params).forEach(([key, value]) => {
			searchParams.append(key, String(value));
		});
		fullUrl += `?${searchParams.toString()}`;
	}

	// Generate cache key for GET requests
	const cacheKey = apiResponseCache.generateKey(method, url, params);

	// Check cache for GET requests (unless cache invalidation is requested)
	if (method === "GET" && !invalidateCache) {
		const cachedData = apiResponseCache.get(cacheKey);
		if (cachedData) {
			return cachedData as T;
		}
	}

	// Prepare request headers
	const headers: Record<string, string> = {
		Authorization: `Bearer ${getConfig("apApiKey")}`,
		"Content-Type": "application/json",
		"Accept-Encoding": "application/json",
		Accept: "application/json",
		Version: "2021-04-15",
	};

	try {
		const response = await fetch(fullUrl, {
			method,
			headers,
			body: data ? JSON.stringify(data) : undefined,
		});

		if (!response.ok) {
			const errorText = await response.text();
			let errorMessage: string;

			try {
				const errorData = JSON.parse(errorText);
				errorMessage =
					errorData.message ||
					errorData.error ||
					`HTTP ${response.status}: ${response.statusText}`;
			} catch {
				errorMessage = `HTTP ${response.status}: ${response.statusText}`;
			}

			throw new Error(`AP API Error: ${errorMessage}`);
		}

		const responseData = await response.json();

		// Cache successful GET responses
		if (method === "GET") {
			apiResponseCache.set(cacheKey, responseData);
		}

		// Invalidate related cache entries for mutating operations
		if (method === "POST" || method === "PUT" || method === "DELETE") {
			const invalidatedCount = apiResponseCache.invalidatePattern(url);
			if (invalidatedCount > 0) {
				logDebug(
					`AP API: Invalidated ${invalidatedCount} cache entries for ${method} ${url}`,
				);
			}
		}

		return responseData as T;
	} catch (error) {
		if (error instanceof Error) {
			throw new Error(`AP API Request Failed: ${error.message}`);
		}
		throw new Error("AP API Request Failed: Unknown error");
	}
};

/**
 * Contact operations
 */
export const contactReq = {
	/**
	 * Get a contact by ID
	 */
	get: async (
		id: string,
		invalidateCache?: boolean,
	): Promise<GetAPContactType> => {
		const response = await apRequestV2<{ contact: GetAPContactType }>({
			url: `/contacts/${id}`,
			method: "GET",
			invalidateCache,
		});
		return response.contact;
	},

	/**
	 * Create a new contact
	 */
	create: async (data: PostAPContactType): Promise<GetAPContactType> => {
		const locationId = getConfig("locationID");
		const response = await apRequestV2<{ contact: GetAPContactType }>({
			url: "/contacts/",
			method: "POST",
			data: { ...cleanData(data), locationId },
		});
		return response.contact;
	},

	/**
	 * Upsert a contact (create or update)
	 */
	upsert: async (data: PostAPContactType): Promise<GetAPContactType> => {
		const locationId = getConfig("locationID");
		const response = await apRequestV2<{ contact: GetAPContactType }>({
			url: "/contacts/upsert/",
			method: "POST",
			data: { ...cleanData(data), locationId },
		});
		return response.contact;
	},

	/**
	 * Update an existing contact
	 */
	update: async (
		id: string,
		data: PostAPContactType,
	): Promise<GetAPContactType> => {
		const response = await apRequestV2<{ contact: GetAPContactType }>({
			url: `/contacts/${id}`,
			method: "PUT",
			data: cleanData(data),
		});
		return response.contact;
	},

	/**
	 * Delete a contact
	 */
	delete: async (id: string): Promise<DeleteResponse> => {
		return apRequestV2<DeleteResponse>({
			url: `/contacts/${id}/`,
			method: "DELETE",
		});
	},

	/**
	 * Get appointments for a contact
	 */
	appointments: async (
		contactId: string,
		invalidateCache?: boolean,
	): Promise<{ appointments: GetAPAppointmentType[] }> => {
		return apRequestV2<{ appointments: GetAPAppointmentType[] }>({
			url: `/contacts/${contactId}/appointments/`,
			method: "GET",
			invalidateCache,
		});
	},

	/**
	 * Get all contacts with optional filtering
	 */
	all: async (params?: {
		limit?: number;
		query?: string;
		startAfter?: number;
		startAfterId?: string;
		invalidateCache?: boolean;
	}): Promise<GetAPContactType[]> => {
		const { invalidateCache, ...queryParams } = params || {};
		const locationId = getConfig("locationID") as string;
		const response = await apRequestV2<{ contacts: GetAPContactType[] }>({
			url: "/contacts/",
			method: "GET",
			params: { ...queryParams, locationId },
			invalidateCache,
		});
		return response.contacts;
	},
};

/**
 * Custom field operations
 */
export const apCustomfield = {
	/**
	 * Get a custom field by ID
	 */
	get: async (
		id: string,
		invalidateCache?: boolean,
	): Promise<APGetCustomFieldType> => {
		const locationId = getConfig("locationID");
		const response = await apRequestV2<{ customField: APGetCustomFieldType }>({
			url: `/locations/${locationId}/customFields/${id}/`,
			method: "GET",
			invalidateCache,
		});
		return response.customField;
	},

	/**
	 * Get all custom fields
	 */
	all: async (invalidateCache?: boolean): Promise<APGetCustomFieldType[]> => {
		const locationId = getConfig("locationID");
		const response = await apRequestV2<{
			customFields: APGetCustomFieldType[];
		}>({
			url: `/locations/${locationId}/customFields/?model=all`,
			method: "GET",
			invalidateCache,
		});
		return response.customFields;
	},

	/**
	 * Create a new custom field using the location-specific endpoint
	 * Note: The v2 /custom-fields/ endpoint only supports Custom Objects and Company fields,
	 * not Contact fields yet. We must use the location-specific endpoint for contacts.
	 *
	 * Enhanced with production-ready validation and error handling.
	 */
	create: async (
		data: APPostCustomfieldType,
	): Promise<APGetCustomFieldType> => {
		// Input validation
		if (!data) {
			throw new Error("AP Custom Field creation data is required");
		}

		if (!data.name || data.name.trim().length === 0) {
			throw new Error("AP Custom Field name is required and cannot be empty");
		}

		if (!data.dataType || data.dataType.trim().length === 0) {
			throw new Error("AP Custom Field dataType is required and cannot be empty");
		}

		// Validate field name doesn't contain invalid characters
		const invalidChars = /[<>:"\/\\|?*]/;
		if (invalidChars.test(data.name)) {
			throw new Error(`AP Custom Field name "${data.name}" contains invalid characters`);
		}

		const locationId = getConfig("locationID");
		if (!locationId) {
			throw new Error("Location ID is required for AP custom field creation");
		}

		// Clean and prepare data
		const cleanedData = cleanData(data);

		try {
			const response = await apRequestV2<{ customField: APGetCustomFieldType }>({
				url: `/locations/${locationId}/customFields/`,
				method: "POST",
				data: cleanedData,
			});

			if (!response.customField) {
				throw new Error("AP API returned invalid response: missing customField");
			}

			return response.customField;
		} catch (error) {
			// Enhanced error handling with context
			if (error instanceof Error) {
				throw new Error(`Failed to create AP custom field "${data.name}": ${error.message}`);
			}
			throw new Error(`Failed to create AP custom field "${data.name}": Unknown error`);
		}
	},

	/**
	 * Update an existing custom field
	 */
	update: async (
		id: string,
		data: APPostCustomfieldType,
	): Promise<APGetCustomFieldType> => {
		const locationId = getConfig("locationID");
		const response = await apRequestV2<{ customField: APGetCustomFieldType }>({
			url: `/locations/${locationId}/customFields/${id}/`,
			method: "PUT",
			data,
		});
		return response.customField;
	},

	/**
	 * Delete a custom field
	 */
	delete: async (id: string): Promise<boolean> => {
		const locationId = getConfig("locationID");
		const response = await apRequestV2<{ succeded: boolean }>({
			url: `/locations/${locationId}/customFields/${id}/`,
			method: "DELETE",
		});
		return response.succeded;
	},
};

/**
 * Appointment operations
 */
export const apAppointmentReq = {
	/**
	 * Get an appointment by ID
	 */
	get: async (
		apId: string,
		invalidateCache?: boolean,
	): Promise<GetAPAppointmentType> => {
		const response = await apRequestV2<{ appointment: GetAPAppointmentType }>({
			url: `/calendars/events/appointments/${apId}`,
			method: "GET",
			invalidateCache,
		});
		return response.appointment;
	},

	/**
	 * Create a new appointment
	 */
	post: async (
		payload: PostAPAppointmentType,
	): Promise<GetAPAppointmentType> => {
		const calendarId = getConfig("apCalendarId");
		const locationId = getConfig("locationID");
		return apRequestV2({
			url: "/calendars/events/appointments",
			method: "POST",
			data: { calendarId, ...payload, locationId },
		});
	},

	/**
	 * Create a block slot appointment
	 */
	postBlockSlot: async (
		payload: PostAPAppointmentType,
	): Promise<GetAPAppointmentType> => {
		const calendarId = getConfig("apCalendarId");
		const locationId = getConfig("locationID");
		return apRequestV2({
			url: "/calendars/events/block-slots",
			method: "POST",
			data: { calendarId, ...payload, locationId },
		});
	},

	/**
	 * Update an existing appointment
	 */
	put: async (
		apId: string,
		payload: PutAPAppointmentType,
	): Promise<AppointmentUpdateResponse> => {
		const calendarId = getConfig("apCalendarId");
		const locationId = getConfig("locationID");
		return apRequestV2<AppointmentUpdateResponse>({
			url: `/calendars/events/appointments/${apId}`,
			method: "PUT",
			data: { calendarId, ...payload, locationId },
		});
	},

	/**
	 * Delete an appointment
	 */
	delete: async (apId: string): Promise<boolean> => {
		const response = await apRequestV2<{ succeeded: boolean }>({
			url: `/calendars/events/${apId}`,
			method: "DELETE",
		});
		return response.succeeded;
	},
};

/**
 * Note operations
 */
export const apNoteReq = {
	/**
	 * Create a new note for a contact
	 */
	post: async (contactId: string, payload: string): Promise<GetAPNoteType> => {
		const response = await apRequestV2<{ note: GetAPNoteType }>({
			url: `/contacts/${contactId}/notes/`,
			method: "POST",
			data: {
				body: payload,
			},
		});
		return response.note;
	},

	/**
	 * Update an existing note
	 */
	put: async (
		contactId: string,
		noteId: string,
		payload: string,
	): Promise<GetAPNoteType> => {
		const response = await apRequestV2<{ note: GetAPNoteType }>({
			url: `/contacts/${contactId}/notes/${noteId}/`,
			method: "PUT",
			data: {
				body: payload,
			},
		});
		return response.note;
	},

	/**
	 * Delete a note
	 */
	delete: async (contactId: string, noteId: string): Promise<boolean> => {
		const response = await apRequestV2<{ succeded: boolean }>({
			url: `/contacts/${contactId}/notes/${noteId}/`,
			method: "DELETE",
		});
		return response.succeded;
	},
};
