/**
 * CliniCore to AutoPatient Custom Field Conversion Utility
 *
 * Transforms CliniCore custom field structures to AutoPatient format with
 * bidirectional compatibility, comprehensive logging, and graceful error handling.
 *
 * **Key Conversion Rules:**
 * - CC boolean → AP RADIO (Yes/No options)
 * - <PERSON> select-or-custom → AP SINGLE_OPTIONS
 * - <PERSON> select (allowMultipleValues: true) → AP MULTIPLE_OPTIONS
 * - <PERSON> select (allowMultipleValues: false) → AP RADIO
 * - CC text/textarea/email/telephone → AP TEXT
 * - Fallback: unmappable types → AP TEXT
 *
 * **Features:**
 * - Strict TypeScript compliance (no `any` usage)
 * - Reversible transformations for data integrity
 * - Comprehensive error handling with request ID tracing
 * - Performance-optimized for bulk conversions
 * - Detailed logging for debugging and monitoring
 *
 * @since 1.0.0
 * @version 1.0.0
 */

import type { GetCCCustomField, APPostCustomfieldType } from "@type";
import { logCustomField, logWarn } from "@/utils/logger";

/**
 * Convert CliniCore custom field to AutoPatient format
 *
 * Performs intelligent field type conversion with bidirectional compatibility.
 * Handles edge cases gracefully and provides detailed logging for traceability.
 * Supports dynamic option synchronization and field type evolution.
 *
 * @param ccField - CliniCore custom field object to convert
 * @returns AutoPatient custom field format ready for API submission
 *
 * @example
 * ```typescript
 * // Convert CC boolean field to AP radio with Yes/No options
 * const ccBooleanField: GetCCCustomField = {
 *   id: 1,
 *   name: "newsletter-wanted",
 *   label: "Newsletter erwünscht",
 *   type: "boolean",
 *   allowMultipleValues: false,
 *   allowedValues: [],
 *   // ... other properties
 * };
 *
 * const apField = ccToApCustomFieldConvert(ccBooleanField);
 * // Result: { name: "Newsletter erwünscht", dataType: "RADIO", options: ["Yes", "No"], ... }
 *
 * // Convert CC select with multiple values to AP multiple options
 * const ccSelectField: GetCCCustomField = {
 *   id: 2,
 *   name: "interests",
 *   label: "Interests",
 *   type: "select",
 *   allowMultipleValues: true,
 *   allowedValues: [
 *     { id: 1, value: "Sports", ... },
 *     { id: 2, value: "Music", ... }
 *   ],
 *   // ... other properties
 * };
 *
 * const apMultiField = ccToApCustomFieldConvert(ccSelectField);
 * // Result: { dataType: "MULTIPLE_OPTIONS", options: ["Sports", "Music"], ... }
 * ```
 */
export function ccToApCustomFieldConvert(
	ccField: GetCCCustomField,
): APPostCustomfieldType {
	logCustomField("CC→AP conversion started", ccField.name, {
		ccFieldType: ccField.type,
		allowMultiple: ccField.allowMultipleValues,
		hasAllowedValues: Boolean(ccField.allowedValues?.length),
		valueCount: ccField.allowedValues?.length || 0,
	});

	// Base field structure with common properties
	const baseField: APPostCustomfieldType = {
		name: ccField.label || ccField.name, // Prefer label over name for display
		dataType: "TEXT", // Default fallback type
		placeholder: "",
	};

	// Handle CC boolean field type
	if (ccField.type === "boolean") {
		return handleCcBooleanField(ccField, baseField);
	}

	// Handle CC select-or-custom field type
	if (ccField.type === "select-or-custom") {
		return handleCcSelectOrCustomField(ccField, baseField);
	}

	// Handle CC select field type
	if (ccField.type === "select") {
		return handleCcSelectField(ccField, baseField);
	}

	// Handle CC text-based field types
	if (isTextBasedField(ccField.type)) {
		logCustomField("CC→AP text-based conversion", ccField.name, {
			originalType: ccField.type,
		});

		return {
			...baseField,
			dataType: "TEXT",
		};
	}

	// Fallback for unmappable field types
	logWarn(`CC→AP fallback conversion for unmappable type: ${ccField.type}`, {
		fieldName: ccField.name,
		originalType: ccField.type,
		convertedType: "TEXT",
	});

	return {
		...baseField,
		dataType: "TEXT",
	};
}

/**
 * Handle CC boolean field conversion
 *
 * Converts CC boolean fields to AP RADIO with standardized Yes/No options.
 * Ensures reversible transformation back to boolean type and maintains
 * compatibility with the enhanced 2-option detection logic.
 *
 * @param ccField - CC field with boolean type
 * @param baseField - Base AP field structure
 * @returns AP RADIO field with Yes/No options
 */
function handleCcBooleanField(
	ccField: GetCCCustomField,
	baseField: APPostCustomfieldType,
): APPostCustomfieldType {
	logCustomField("CC→AP boolean→RADIO conversion", ccField.name, {
		standardizedOptions: ["Yes", "No"],
		reversibleConversion: true,
		compatibleWith2OptionDetection: true,
	});

	return {
		...baseField,
		dataType: "RADIO",
		options: ["Yes", "No"],
	};
}

/**
 * Handle CC select-or-custom field conversion
 *
 * Converts CC select-or-custom fields to AP SINGLE_OPTIONS.
 * Preserves all allowed values for user selection and handles
 * dynamic option updates inherent in select-or-custom fields.
 *
 * @param ccField - CC field with select-or-custom type
 * @param baseField - Base AP field structure
 * @returns AP SINGLE_OPTIONS field with preserved options
 */
function handleCcSelectOrCustomField(
	ccField: GetCCCustomField,
	baseField: APPostCustomfieldType,
): APPostCustomfieldType {
	const options = extractOptionsFromAllowedValues(ccField.allowedValues);

	logCustomField("CC→AP select-or-custom→SINGLE_OPTIONS conversion", ccField.name, {
		optionCount: options.length,
		options: options,
		dynamicOptionsSupported: true,
		customOptionsAllowed: true,
	});

	// Log about dynamic nature of select-or-custom fields
	logCustomField("Dynamic option capability detected", ccField.name, {
		fieldType: "select-or-custom",
		supportsUserAddedOptions: true,
		recommendation: "monitor_for_new_custom_options",
	});

	return {
		...baseField,
		dataType: "SINGLE_OPTIONS",
		options: options,
	};
}

/**
 * Handle CC select field conversion
 *
 * Converts CC select fields to either AP MULTIPLE_OPTIONS or AP RADIO
 * based on the allowMultipleValues setting. Preserves selection behavior
 * and handles dynamic option updates.
 *
 * @param ccField - CC field with select type
 * @param baseField - Base AP field structure
 * @returns AP field with appropriate selection type
 */
function handleCcSelectField(
	ccField: GetCCCustomField,
	baseField: APPostCustomfieldType,
): APPostCustomfieldType {
	const options = extractOptionsFromAllowedValues(ccField.allowedValues);

	if (ccField.allowMultipleValues) {
		logCustomField("CC→AP select→MULTIPLE_OPTIONS conversion", ccField.name, {
			optionCount: options.length,
			allowMultiple: true,
			dynamicOptionsSupported: true,
		});

		// Check for dynamic option patterns
		detectDynamicOptionPatterns(options, ccField.name);

		return {
			...baseField,
			dataType: "MULTIPLE_OPTIONS",
			options: options,
		};
	}

	// Single selection converts to RADIO
	logCustomField("CC→AP select→RADIO conversion", ccField.name, {
		optionCount: options.length,
		allowMultiple: false,
		willBeBooleanCandidate: options.length === 2,
	});

	// Log if this will be detected as boolean in reverse conversion
	if (options.length === 2) {
		logCustomField("2-option select detected - will be boolean candidate in AP→CC conversion", ccField.name, {
			options: options,
			reversibleToBooleanType: true,
		});
	}

	// Check for dynamic option patterns
	detectDynamicOptionPatterns(options, ccField.name);

	return {
		...baseField,
		dataType: "RADIO",
		options: options,
	};
}

/**
 * Check if CC field type is text-based
 *
 * Identifies CC field types that should convert to AP TEXT fields.
 * Supports various text input types for comprehensive coverage.
 *
 * @param fieldType - CC field type to check
 * @returns True if field type is text-based
 *
 * @example
 * ```typescript
 * isTextBasedField("text") // true
 * isTextBasedField("textarea") // true
 * isTextBasedField("email") // true
 * isTextBasedField("telephone") // true
 * isTextBasedField("number") // true
 * isTextBasedField("select") // false
 * isTextBasedField("boolean") // false
 * ```
 */
function isTextBasedField(fieldType: string): boolean {
	const textBasedTypes = [
		"text",
		"textarea", 
		"email",
		"telephone",
		"number",
		"medication",
		"permanent-diagnoses",
		"patient-has-recommended",
	];

	return textBasedTypes.includes(fieldType);
}

/**
 * Extract option values from CC allowedValues array
 *
 * Safely extracts string values from CC allowedValues structure.
 * Handles missing or malformed data gracefully.
 *
 * @param allowedValues - CC allowedValues array
 * @returns Array of option strings for AP field
 *
 * @example
 * ```typescript
 * const ccValues = [
 *   { id: 1, value: "Option A", ... },
 *   { id: 2, value: "Option B", ... }
 * ];
 *
 * extractOptionsFromAllowedValues(ccValues) // ["Option A", "Option B"]
 * extractOptionsFromAllowedValues([]) // []
 * extractOptionsFromAllowedValues(undefined) // []
 * ```
 */
function extractOptionsFromAllowedValues(
	allowedValues: GetCCCustomField["allowedValues"],
): string[] {
	if (!allowedValues || !Array.isArray(allowedValues)) {
		return [];
	}

	return allowedValues
		.map((item) => item.value)
		.filter((value): value is string => typeof value === "string" && value.length > 0);
}

/**
 * Detect and log potential dynamic option patterns in CC fields
 *
 * Analyzes option patterns to identify fields that may support dynamic
 * option addition, removal, or modification after initial creation.
 * Helps with monitoring field evolution and synchronization planning.
 *
 * @param options - Current option array
 * @param fieldName - Field name for logging
 */
function detectDynamicOptionPatterns(
	options: string[],
	fieldName: string,
): void {
	// Check for patterns that suggest dynamic updates
	const hasNumberedOptions = options.some(opt => /^\d+\./.test(opt));
	const hasVersionedOptions = options.some(opt => /v\d+|version/i.test(opt));
	const hasDateOptions = options.some(opt => /\d{4}|\d{2}\/\d{2}/.test(opt));
	const hasStatusOptions = options.some(opt => /active|inactive|pending|draft/i.test(opt));
	const hasEmptyOptions = options.length === 0;

	if (hasNumberedOptions || hasVersionedOptions || hasDateOptions || hasStatusOptions || hasEmptyOptions) {
		logCustomField("Dynamic option pattern detected in CC field", fieldName, {
			hasNumberedOptions,
			hasVersionedOptions,
			hasDateOptions,
			hasStatusOptions,
			hasEmptyOptions,
			optionCount: options.length,
			recommendation: "monitor_for_option_updates",
			syncConsideration: "field_may_evolve_over_time",
		});
	}

	// Log option mismatch potential
	if (options.length > 20) {
		logWarn("Large option set detected - potential for option mismatches during sync", {
			fieldName,
			optionCount: options.length,
			recommendation: "implement_option_diff_detection",
		});
	}
}

export default ccToApCustomFieldConvert;
